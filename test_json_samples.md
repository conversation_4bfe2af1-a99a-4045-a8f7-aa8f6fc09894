# JSON测试样例

## 修复前会报错的JSON（单个整数值）

```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "processingMode": "BATCH_ROWS",
  "targetRowIndexes": 1,
  "updateBitableWithLocalUrl": true,
  "summaryOnly": true
}
```

## 正常的JSON（数组值）

```json
{
  "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
  "tableId": "tbl4sH8PYHUk36K0",
  "fieldMapping": {
    "👚上装正面图": "👚上装正面图url"
  },
  "processingMode": "BATCH_ROWS",
  "targetRowIndexes": [1, 3, 5],
  "updateBitableWithLocalUrl": true,
  "summaryOnly": true
}
```

## 使用curl测试命令

### 测试单个整数值（修复后应该正常）
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }'
```

### 测试数组值（应该继续正常工作）
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": [1, 3, 5],
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }'
```

## 错误原因分析

原始错误：
```
Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from Integer value (token `JsonToken.VALUE_NUMBER_INT`)
```

**问题原因**：
- Jackson默认期望 `List<Integer>` 字段接收JSON数组格式：`[1, 2, 3]`
- 当传递单个整数值 `1` 时，Jackson无法将其反序列化为List

**解决方案**：
- 添加了自定义反序列化器 `IntegerListDeserializer`
- 支持三种输入格式：
  1. 单个整数：`"targetRowIndexes": 1` → `[1]`
  2. 数组：`"targetRowIndexes": [1, 3, 5]` → `[1, 3, 5]`
  3. null值：`"targetRowIndexes": null` → `null`

**实现细节**：
```java
@JsonDeserialize(using = IntegerListDeserializer.class)
private List<Integer> targetRowIndexes;
```

这样用户可以更灵活地传递参数，既可以传单个值也可以传数组。
