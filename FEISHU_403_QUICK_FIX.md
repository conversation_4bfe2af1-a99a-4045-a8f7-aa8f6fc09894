# 🚨 飞书API 403权限错误快速修复指南

## 🔍 错误分析

您遇到的错误：
```
飞书API响应状态码: 403
飞书API响应内容: {"code":91403,"msg":"Forbidden","data":{}}
```

**错误代码 91403** = 权限被拒绝，应用无法访问目标资源。

## ⚡ 快速修复步骤

### 步骤1: 检查飞书开放平台权限 🔑

1. **登录飞书开放平台**
   - 访问：https://open.feishu.cn/
   - 使用您的飞书账号登录

2. **进入应用管理**
   - 找到您的应用：`cli_a8fe3e73bd78d00d`
   - 点击进入应用详情

3. **检查API权限**
   - 点击左侧菜单 "权限管理" → "API权限"
   - 确保已申请并获得以下权限：
     ```
     ✅ bitable:app - 多维表格应用权限
     ✅ bitable:app:readonly - 多维表格只读权限  
     ✅ bitable:app:readwrite - 多维表格读写权限
     ```

4. **如果权限未申请**
   - 点击"申请权限"
   - 搜索"多维表格"相关权限
   - 提交申请并等待审核

### 步骤2: 检查表格授权 📋

1. **打开目标多维表格**
   - App Token: `LgSGbXDBsa0yCsshXujcN7WUnCh`
   - 在飞书中打开对应的多维表格

2. **检查应用授权**
   - 点击表格右上角的 "..." 菜单
   - 选择 "高级设置" → "开放平台"
   - 查看"已授权应用"列表

3. **添加应用授权**（如果应用不在列表中）
   - 点击 "添加应用"
   - 搜索并选择您的应用
   - 确认授权

### 步骤3: 验证修复 🧪

运行诊断脚本：
```bash
./diagnose_feishu_permissions.sh
```

或手动测试：
```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "pageSize": 1
  }'
```

## 🔧 常见问题解决

### 问题1: 权限申请被拒绝
**解决方案：**
- 检查应用类型是否支持多维表格功能
- 确保应用描述清楚说明了使用多维表格的目的
- 如果是企业应用，可能需要企业管理员审核

### 问题2: 表格无法找到授权选项
**解决方案：**
- 确保您是表格的创建者或管理员
- 检查表格的共享设置
- 尝试重新创建一个测试表格

### 问题3: 权限已申请但仍然403
**解决方案：**
- 等待权限生效（可能需要几分钟到几小时）
- 重新获取Token：访问 `http://localhost:8080/api/token/app`
- 检查Token是否过期

### 问题4: 企业内部应用权限问题
**解决方案：**
- 联系企业飞书管理员
- 确认企业是否允许第三方应用访问
- 检查企业的安全策略设置

## 📝 测试用例

### 测试1: 基础权限测试
```bash
# 测试Token获取
curl -X GET "http://localhost:8080/api/token/app"

# 期望结果：返回包含access_token的JSON
```

### 测试2: 表格访问测试
```bash
# 测试表格记录获取
curl -X POST "http://localhost:8080/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "pageSize": 1
  }'

# 期望结果：返回code=0的成功响应
```

### 测试3: 增强版接口测试
```bash
# 测试增强版字段映射接口
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_PAGE",
    "summaryOnly": true
  }'

# 期望结果：不再出现403错误
```

## 🆘 如果问题仍然存在

### 联系飞书技术支持
- **官方文档**：https://open.feishu.cn/document/
- **开发者社区**：https://open.feishu.cn/community/
- **技术支持**：通过飞书开放平台提交工单

### 提供以下信息
1. **应用ID**：`cli_a8fe3e73bd78d00d`
2. **错误代码**：`91403`
3. **具体错误信息**：`Forbidden`
4. **尝试的解决方案**：列出您已经尝试的步骤
5. **应用类型**：企业内部应用/第三方应用

## 📋 权限检查清单

- [ ] 飞书开放平台权限已申请并通过
- [ ] 多维表格已授权给应用
- [ ] Token获取正常
- [ ] 应用状态正常（已发布/开发中）
- [ ] 表格访问权限正确
- [ ] 企业安全策略允许（如适用）

## 🎯 预期结果

修复完成后，您应该能够：
1. ✅ 正常获取多维表格记录
2. ✅ 使用增强版字段映射接口
3. ✅ 不再出现403权限错误
4. ✅ 正常处理图片字段映射

---

**💡 提示**：权限问题通常是配置问题，按照上述步骤逐一检查，大部分情况下都能解决。如果是新创建的应用，建议先用一个简单的测试表格验证权限配置是否正确。
