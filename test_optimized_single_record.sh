#!/bin/bash

# 优化的单记录获取测试脚本
# 展示如何精确获取特定行数据，避免获取全部数据

BASE_URL="http://localhost:8080"
ENDPOINT="/api/feishu/bitable/images/field-mapping-upload-enhanced"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}优化的单记录获取测试${NC}"
echo -e "${BLUE}========================================${NC}"

# 测试用的表格信息
APP_TOKEN="LgSGbXDBsa0yCsshXujcN7WUnCh"
TABLE_ID="tblUkixc2NcTbpmX"

echo -e "\n${YELLOW}测试配置:${NC}"
echo -e "Base URL: ${BASE_URL}"
echo -e "Endpoint: ${ENDPOINT}"
echo -e "App Token: ${APP_TOKEN}"
echo -e "Table ID: ${TABLE_ID}"

echo -e "\n${YELLOW}优化说明:${NC}"
echo -e "1. SINGLE_RECORD模式：使用飞书单记录API，只获取指定记录"
echo -e "2. 传统BATCH_ROWS模式：先获取所有数据，再在内存中筛选"
echo -e "3. 对比两种方式的性能差异"

# 测试1: 使用SINGLE_RECORD模式 + 记录ID（最优化）
echo -e "\n${BLUE}测试1: SINGLE_RECORD模式 + 记录ID（最优化）${NC}"
echo -e "${YELLOW}直接通过记录ID精确获取，不获取其他数据${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_RECORD",
    "targetRecordIds": ["rec123456"],
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试1完成${NC}"

# 测试2: 使用SINGLE_RECORD模式 + 行索引（优化）
echo -e "\n${BLUE}测试2: SINGLE_RECORD模式 + 行索引（优化）${NC}"
echo -e "${YELLOW}通过行索引找到记录ID，然后精确获取${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_RECORD",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试2完成${NC}"

# 测试3: 传统BATCH_ROWS模式对比
echo -e "\n${BLUE}测试3: 传统BATCH_ROWS模式（对比）${NC}"
echo -e "${YELLOW}获取所有数据后在内存中筛选第1行${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试3完成${NC}"

# 测试4: 批量记录ID优化
echo -e "\n${BLUE}测试4: 单个记录ID的批量模式优化${NC}"
echo -e "${YELLOW}BATCH_ROWS模式检测到单个记录ID时自动使用精确获取${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "BATCH_ROWS",
    "targetRecordIds": ["rec123456"],
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试4完成${NC}"

# 测试5: 性能对比测试
echo -e "\n${BLUE}测试5: 性能对比测试${NC}"
echo -e "${YELLOW}比较不同模式的处理时间${NC}"

echo -e "\n${YELLOW}5.1 SINGLE_RECORD模式性能测试${NC}"
time curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_RECORD",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' > /dev/null

echo -e "\n${YELLOW}5.2 BATCH_ROWS模式性能测试${NC}"
time curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' > /dev/null

echo -e "\n${GREEN}性能测试完成${NC}"

echo -e "\n${BLUE}========================================${NC}"
echo -e "${GREEN}所有测试完成！${NC}"
echo -e "${BLUE}========================================${NC}"

echo -e "\n${YELLOW}优化效果说明:${NC}"
echo -e "1. ${GREEN}SINGLE_RECORD + recordId${NC}: 最优化，直接获取单条记录"
echo -e "2. ${GREEN}SINGLE_RECORD + rowIndex${NC}: 优化，先定位recordId再精确获取"
echo -e "3. ${GREEN}BATCH_ROWS + 单个recordId${NC}: 自动优化，检测到单记录时使用精确获取"
echo -e "4. ${YELLOW}BATCH_ROWS + rowIndex${NC}: 传统方式，获取所有数据后筛选"

echo -e "\n${YELLOW}使用建议:${NC}"
echo -e "• 如果知道记录ID，使用 ${GREEN}SINGLE_RECORD + targetRecordIds${NC}"
echo -e "• 如果只知道行号，使用 ${GREEN}SINGLE_RECORD + targetRowIndexes${NC}"
echo -e "• 处理多条记录时，使用 ${GREEN}BATCH_ROWS${NC} 模式"
echo -e "• 大数据量表格中处理单条记录时，避免使用传统的全量获取方式"

echo -e "\n${YELLOW}API参数对比:${NC}"
echo -e "传统方式: processingMode: 'BATCH_ROWS', targetRowIndexes: 1"
echo -e "优化方式: processingMode: 'SINGLE_RECORD', targetRowIndexes: 1"
echo -e "最优方式: processingMode: 'SINGLE_RECORD', targetRecordIds: ['rec123']"
