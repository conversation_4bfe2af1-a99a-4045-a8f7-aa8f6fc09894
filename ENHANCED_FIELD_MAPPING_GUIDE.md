# 🚀 增强版字段映射图片上传接口使用指南

## 📋 接口概述

增强版字段映射图片上传接口 `/api/feishu/bitable/images/field-mapping-upload-enhanced` 是在原有字段映射接口基础上的重大升级，专门解决以下问题：

1. **突破500行限制**：支持自动分页处理大数据集
2. **精确行控制**：支持指定特定行索引或记录ID进行处理
3. **性能优化**：支持批处理、并发控制、仅返回摘要等优化功能
4. **灵活配置**：支持跳过已有URL、自定义超时时间等高级配置

## 🎯 核心功能

### 1. 三种处理模式

| 模式 | 说明 | 适用场景 |
|------|------|----------|
| `SINGLE_PAGE` | 单页处理模式 | 处理单页数据，默认模式 |
| `BATCH_ROWS` | 批量行处理模式 | 处理指定的行索引、记录ID或行范围 |
| `AUTO_PAGINATION` | 自动分页处理模式 | 自动处理多页数据，突破500行限制 |

### 2. 行选择方式

- **行索引列表**：`targetRowIndexes: [1, 3, 5, 10]`
- **记录ID列表**：`targetRecordIds: ["recqwIwhc6", "recABC123"]`
- **行范围**：`startRowIndex: 10, endRowIndex: 50`

### 3. 性能优化功能

- **仅返回摘要**：`summaryOnly: true` - 不返回详细记录信息
- **跳过已有URL**：`skipExistingUrls: true` - 避免重复处理
- **并发控制**：`maxConcurrentDownloads: 5` - 控制下载并发数
- **批处理大小**：`batchSize: 10` - 控制每批处理的记录数

## 📝 请求参数详解

### 基础参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| appToken | String | ✅ | 多维表格的唯一标识符 | "MgDxby4r7avigssLQnVcIQzJnm1" |
| tableId | String | ✅ | 数据表的唯一标识符 | "tbl4sH8PYHUk36K0" |
| fieldMapping | Map | ✅ | 字段映射关系 | {"👚上装正面图": "👚上装正面图url"} |

### 行选择参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| targetRowIndexes | List<Integer> | ❌ | 指定要处理的行索引列表（从1开始） | [1, 3, 5, 10] |
| targetRecordIds | List<String> | ❌ | 指定要处理的记录ID列表 | ["recqwIwhc6", "recABC123"] |
| startRowIndex | Integer | ❌ | 起始行索引（从1开始） | 10 |
| endRowIndex | Integer | ❌ | 结束行索引（从1开始） | 50 |

### 分页控制参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| pageSize | Integer | ❌ | 分页大小，最大500 | 100 |
| pageToken | String | ❌ | 分页标记 | "eVQrYD0ga2RhbGlWaVdWN0tRdHRRWVJ" |
| autoHandlePagination | Boolean | ❌ | 是否自动处理分页 | true |
| maxPagesToProcess | Integer | ❌ | 最大处理页数 | 10 |

### 性能优化参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| summaryOnly | Boolean | ❌ | 是否只返回处理结果摘要 | false |
| downloadTimeout | Integer | ❌ | 图片下载超时时间（秒） | 30 |
| maxConcurrentDownloads | Integer | ❌ | 最大并发下载数 | 5 |
| batchSize | Integer | ❌ | 批处理大小 | 10 |

### 功能控制参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| updateBitableWithLocalUrl | Boolean | ❌ | 是否将本地URL写回多维表格 | true |
| includeImageDetails | Boolean | ❌ | 是否返回图片的详细信息 | true |
| skipExistingUrls | Boolean | ❌ | 是否跳过已有本地URL的字段 | true |
| processingMode | String | ❌ | 处理模式 | "SINGLE_PAGE" |

## 🔧 使用示例

### 示例1: 基本字段映射（单页处理）

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "processingMode": "SINGLE_PAGE",
    "updateBitableWithLocalUrl": true
  }'
```

### 示例2: 处理特定行索引

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": [1, 3, 5, 10],
    "updateBitableWithLocalUrl": true
  }'
```

### 示例3: 处理行范围

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "processingMode": "BATCH_ROWS",
    "startRowIndex": 10,
    "endRowIndex": 50,
    "updateBitableWithLocalUrl": true
  }'
```

### 示例4: 自动分页处理大数据集

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "AUTO_PAGINATION",
    "autoHandlePagination": true,
    "maxPagesToProcess": 10,
    "pageSize": 100,
    "summaryOnly": true,
    "updateBitableWithLocalUrl": true
  }'
```

### 示例5: 性能优化模式

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "MgDxby4r7avigssLQnVcIQzJnm1",
    "tableId": "tbl4sH8PYHUk36K0",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "👖下装正面图": "👖下装正面图url"
    },
    "processingMode": "SINGLE_PAGE",
    "summaryOnly": true,
    "skipExistingUrls": true,
    "maxConcurrentDownloads": 3,
    "downloadTimeout": 15,
    "updateBitableWithLocalUrl": false
  }'
```

## 📊 响应格式

### 成功响应示例

```json
{
  "code": 200,
  "message": "处理成功",
  "success": true,
  "data": {
    "summary": {
      "totalRecords": 25,
      "successfulRecords": 23,
      "failedRecords": 2,
      "skippedRecords": 0,
      "totalImages": 45,
      "successfulDownloads": 42,
      "failedDownloads": 3,
      "skippedImages": 0,
      "processedFieldMappings": 2
    },
    "pagination": {
      "currentPageSize": 25,
      "hasMore": false,
      "nextPageToken": null,
      "processedPages": 1
    },
    "processingTime": 15420,
    "processingMode": "SINGLE_PAGE",
    "records": [
      {
        "recordId": "recqwIwhc6",
        "rowIndex": 1,
        "status": "SUCCESS",
        "message": "所有字段处理成功",
        "imageCount": 2,
        "successCount": 2,
        "failureCount": 0,
        "fieldResults": {
          "👚上装正面图": {
            "sourceField": "👚上装正面图",
            "targetField": "👚上装正面图url",
            "status": "SUCCESS",
            "message": "处理完成，成功: 1, 失败: 0",
            "imageCount": 1,
            "localUrls": ["http://localhost:8080/images/feishu/20241201_143022_image1.jpg"]
          }
        }
      }
    ]
  }
}
```

## ⚡ 性能建议

1. **大数据集处理**：使用 `AUTO_PAGINATION` 模式，设置合理的 `maxPagesToProcess`
2. **精确处理**：使用 `BATCH_ROWS` 模式指定特定行或范围
3. **性能优化**：设置 `summaryOnly: true` 减少响应数据量
4. **避免重复**：设置 `skipExistingUrls: true` 跳过已处理的字段
5. **并发控制**：根据服务器性能调整 `maxConcurrentDownloads`

## 🔄 与原接口的对比

| 功能 | 原接口 | 增强版接口 |
|------|--------|------------|
| 500行限制 | ❌ 受限 | ✅ 自动分页突破 |
| 指定行处理 | ❌ 不支持 | ✅ 支持多种方式 |
| 性能优化 | ❌ 基础功能 | ✅ 多种优化选项 |
| 响应格式 | 基础格式 | 增强格式，包含详细统计 |
| 错误处理 | 基础处理 | 详细的字段级错误信息 |

## 🚨 注意事项

1. **兼容性**：增强版接口不影响原有接口的使用
2. **性能**：大数据集处理时建议使用 `summaryOnly: true`
3. **限制**：单次请求仍受飞书API的500行限制，但可通过自动分页突破
4. **错误处理**：详细的字段级错误信息有助于问题定位
5. **资源消耗**：自动分页模式会消耗更多服务器资源，请合理设置 `maxPagesToProcess`
