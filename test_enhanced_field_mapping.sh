#!/bin/bash

# 增强版字段映射图片上传接口测试脚本
# 测试新的 /api/feishu/bitable/images/field-mapping-upload-enhanced 接口

BASE_URL="http://localhost:8080"
ENDPOINT="/api/feishu/bitable/images/field-mapping-upload-enhanced"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}增强版字段映射图片上传接口测试${NC}"
echo -e "${BLUE}========================================${NC}"

# 测试用的表格信息（请根据实际情况修改）
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"

echo -e "\n${YELLOW}测试配置:${NC}"
echo -e "Base URL: ${BASE_URL}"
echo -e "Endpoint: ${ENDPOINT}"
echo -e "App Token: ${APP_TOKEN}"
echo -e "Table ID: ${TABLE_ID}"

# 测试1: 基本单页处理模式
echo -e "\n${BLUE}测试1: 基本单页处理模式${NC}"
echo -e "${YELLOW}测试字段映射功能，处理单页数据${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "processingMode": "SINGLE_PAGE",
    "pageSize": 10,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": false,
    "includeImageDetails": true
  }' | jq '.'

echo -e "\n${GREEN}测试1完成${NC}"

# 测试2: 批量行处理模式 - 指定行索引（数组）
echo -e "\n${BLUE}测试2: 批量行处理模式 - 指定行索引（数组）${NC}"
echo -e "${YELLOW}只处理第1、3、5行的数据${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": [1, 3, 5],
    "updateBitableWithLocalUrl": true,
    "summaryOnly": false
  }' | jq '.'

echo -e "\n${GREEN}测试2完成${NC}"

# 测试2.5: 批量行处理模式 - 指定单个行索引
echo -e "\n${BLUE}测试2.5: 批量行处理模式 - 指定单个行索引${NC}"
echo -e "${YELLOW}只处理第1行的数据（测试单个整数值）${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": false
  }' | jq '.'

echo -e "\n${GREEN}测试2.5完成${NC}"

# 测试3: 行范围处理
echo -e "\n${BLUE}测试3: 行范围处理${NC}"
echo -e "${YELLOW}处理第2-5行的数据${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url"
    },
    "processingMode": "BATCH_ROWS",
    "startRowIndex": 2,
    "endRowIndex": 5,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试3完成${NC}"

# 测试4: 自动分页处理模式
echo -e "\n${BLUE}测试4: 自动分页处理模式${NC}"
echo -e "${YELLOW}自动处理多页数据，最多处理3页${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "AUTO_PAGINATION",
    "autoHandlePagination": true,
    "maxPagesToProcess": 3,
    "pageSize": 20,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试4完成${NC}"

# 测试5: 性能优化模式
echo -e "\n${BLUE}测试5: 性能优化模式${NC}"
echo -e "${YELLOW}只返回摘要信息，跳过已有URL的字段${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url",
      "👚上装背面图": "👚上装背面图url",
      "👖下装正面图": "👖下装正面图url"
    },
    "processingMode": "SINGLE_PAGE",
    "summaryOnly": true,
    "skipExistingUrls": true,
    "maxConcurrentDownloads": 3,
    "downloadTimeout": 15,
    "updateBitableWithLocalUrl": false
  }' | jq '.'

echo -e "\n${GREEN}测试5完成${NC}"

# 测试6: 错误处理测试
echo -e "\n${BLUE}测试6: 错误处理测试${NC}"
echo -e "${YELLOW}测试无效的appToken${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "invalid_token",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "SINGLE_PAGE"
  }' | jq '.'

echo -e "\n${GREEN}测试6完成${NC}"

echo -e "\n${BLUE}========================================${NC}"
echo -e "${GREEN}所有测试完成！${NC}"
echo -e "${BLUE}========================================${NC}"

echo -e "\n${YELLOW}测试说明:${NC}"
echo -e "1. 基本单页处理: 测试标准的字段映射功能"
echo -e "2. 批量行处理: 测试指定特定行索引的处理"
echo -e "3. 行范围处理: 测试指定行范围的处理"
echo -e "4. 自动分页处理: 测试自动处理多页数据"
echo -e "5. 性能优化: 测试只返回摘要、跳过已有URL等优化功能"
echo -e "6. 错误处理: 测试错误情况的处理"

echo -e "\n${YELLOW}注意事项:${NC}"
echo -e "- 请确保服务器正在运行在 ${BASE_URL}"
echo -e "- 请根据实际情况修改 APP_TOKEN 和 TABLE_ID"
echo -e "- 请确保表格中存在测试用的字段名称"
echo -e "- 如果没有安装 jq，请移除 '| jq \".\"' 部分"
