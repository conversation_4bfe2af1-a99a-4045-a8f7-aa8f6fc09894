#!/bin/bash

# 飞书权限诊断脚本
# 用于诊断和检查飞书API权限问题

BASE_URL="http://localhost:8080"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}飞书API权限诊断工具${NC}"
echo -e "${BLUE}========================================${NC}"

# 测试用的表格信息
APP_TOKEN="LgSGbXDBsa0yCsshXujcN7WUnCh"
TABLE_ID="tblUkixc2NcTbpmX"

echo -e "\n${YELLOW}当前配置信息:${NC}"
echo -e "App Token: ${APP_TOKEN}"
echo -e "Table ID: ${TABLE_ID}"

# 1. 测试Token获取
echo -e "\n${BLUE}步骤1: 测试Token获取${NC}"
echo -e "${YELLOW}检查应用是否能正常获取访问令牌...${NC}"

TOKEN_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/token/app")
echo -e "Token响应: ${TOKEN_RESPONSE}"

if echo "${TOKEN_RESPONSE}" | grep -q "access_token"; then
    echo -e "${GREEN}✅ Token获取成功${NC}"
else
    echo -e "${RED}❌ Token获取失败${NC}"
    echo -e "${RED}请检查飞书应用配置（app_id和app_secret）${NC}"
fi

# 2. 测试基础API访问
echo -e "\n${BLUE}步骤2: 测试基础多维表格访问${NC}"
echo -e "${YELLOW}尝试访问多维表格记录...${NC}"

curl -s -X POST "${BASE_URL}/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "pageSize": 1
  }' > /tmp/bitable_test.json

echo -e "多维表格访问响应:"
cat /tmp/bitable_test.json | jq '.' 2>/dev/null || cat /tmp/bitable_test.json

if grep -q '"code":403' /tmp/bitable_test.json; then
    echo -e "\n${RED}❌ 权限被拒绝 (403 Forbidden)${NC}"
    echo -e "${RED}可能的原因：${NC}"
    echo -e "${RED}1. 应用没有多维表格访问权限${NC}"
    echo -e "${RED}2. 应用没有被授权访问这个特定表格${NC}"
    echo -e "${RED}3. Token权限范围不够${NC}"
elif grep -q '"code":0' /tmp/bitable_test.json; then
    echo -e "\n${GREEN}✅ 多维表格访问成功${NC}"
else
    echo -e "\n${YELLOW}⚠️  未知响应，请检查详细信息${NC}"
fi

# 3. 权限检查建议
echo -e "\n${BLUE}步骤3: 权限配置检查建议${NC}"
echo -e "${YELLOW}请按以下步骤检查飞书开放平台配置：${NC}"

echo -e "\n${BLUE}A. 检查API权限：${NC}"
echo -e "1. 登录飞书开放平台: https://open.feishu.cn/"
echo -e "2. 进入应用管理 → 选择您的应用"
echo -e "3. 点击 '权限管理' → 'API权限'"
echo -e "4. 确保已申请以下权限："
echo -e "   - bitable:app (多维表格应用权限)"
echo -e "   - bitable:app:readonly (多维表格只读权限)"
echo -e "   - bitable:app:readwrite (多维表格读写权限)"

echo -e "\n${BLUE}B. 检查表格授权：${NC}"
echo -e "1. 打开目标多维表格"
echo -e "2. 点击右上角 '...' 菜单"
echo -e "3. 选择 '高级设置' → '开放平台'"
echo -e "4. 确保您的应用在授权列表中"
echo -e "5. 如果没有，点击 '添加应用' 并选择您的应用"

echo -e "\n${BLUE}C. 检查应用状态：${NC}"
echo -e "1. 确保应用已发布或在开发环境中正确配置"
echo -e "2. 检查应用的可用范围设置"
echo -e "3. 确认应用类型支持多维表格功能"

# 4. 常见解决方案
echo -e "\n${BLUE}步骤4: 常见解决方案${NC}"
echo -e "${YELLOW}如果仍然遇到403错误，请尝试：${NC}"

echo -e "\n${GREEN}方案1: 重新申请权限${NC}"
echo -e "1. 在飞书开放平台重新申请多维表格相关权限"
echo -e "2. 等待权限审核通过"
echo -e "3. 重新测试接口"

echo -e "\n${GREEN}方案2: 检查表格共享设置${NC}"
echo -e "1. 确保表格的共享权限设置正确"
echo -e "2. 检查表格是否对应用开放"
echo -e "3. 尝试使用表格管理员账号重新授权"

echo -e "\n${GREEN}方案3: 使用不同的Token类型${NC}"
echo -e "1. 检查是否需要使用tenant_access_token而不是app_access_token"
echo -e "2. 根据具体需求选择合适的Token类型"

echo -e "\n${GREEN}方案4: 联系飞书技术支持${NC}"
echo -e "1. 如果以上方案都无效，建议联系飞书技术支持"
echo -e "2. 提供应用ID和具体的错误信息"

# 5. 测试其他表格（如果有的话）
echo -e "\n${BLUE}步骤5: 建议测试${NC}"
echo -e "${YELLOW}建议使用一个您确定有权限的表格进行测试：${NC}"
echo -e "1. 创建一个新的测试表格"
echo -e "2. 确保应用被正确授权访问该表格"
echo -e "3. 使用新表格的app_token和table_id重新测试"

echo -e "\n${BLUE}========================================${NC}"
echo -e "${GREEN}诊断完成！${NC}"
echo -e "${BLUE}========================================${NC}"

# 清理临时文件
rm -f /tmp/bitable_test.json

echo -e "\n${YELLOW}注意事项：${NC}"
echo -e "- 权限申请可能需要一定时间审核"
echo -e "- 确保使用的app_token和table_id是正确的"
echo -e "- 如果是企业内部应用，可能需要管理员授权"
echo -e "- 某些权限可能需要企业认证才能申请"
