# 🚀 单记录精确获取优化指南

## 🎯 优化目标

解决您提出的核心需求：**不要一次性获取全部数据，只需要获取对应行的数据**

## 📊 优化前后对比

### ❌ 优化前（传统方式）
```bash
# 即使只需要第1行，也会获取所有数据然后筛选
curl -X POST "/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -d '{
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": 1
  }'
```
**问题**：
- 获取所有表格数据（可能数千行）
- 在内存中筛选目标行
- 网络传输量大
- 处理时间长

### ✅ 优化后（精确获取）
```bash
# 只获取第1行数据
curl -X POST "/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -d '{
    "processingMode": "SINGLE_RECORD",
    "targetRowIndexes": 1
  }'
```
**优势**：
- 只获取目标记录
- 网络传输量最小
- 处理速度最快
- 内存占用最少

## 🔧 三种优化方案

### 方案1: 记录ID精确获取（最优）⭐⭐⭐⭐⭐

```json
{
  "appToken": "your_token",
  "tableId": "your_table",
  "fieldMapping": {"源字段": "目标字段"},
  "processingMode": "SINGLE_RECORD",
  "targetRecordIds": ["rec123456"]
}
```

**特点**：
- ✅ 直接使用飞书单记录API
- ✅ 只发送一次网络请求
- ✅ 只获取目标记录数据
- ✅ 性能最优

### 方案2: 行索引智能获取（推荐）⭐⭐⭐⭐

```json
{
  "appToken": "your_token",
  "tableId": "your_table", 
  "fieldMapping": {"源字段": "目标字段"},
  "processingMode": "SINGLE_RECORD",
  "targetRowIndexes": 1
}
```

**特点**：
- ✅ 自动定位记录ID
- ✅ 然后精确获取
- ✅ 比传统方式快很多
- ⚠️ 需要两次网络请求

### 方案3: 自动优化（智能）⭐⭐⭐

```json
{
  "appToken": "your_token",
  "tableId": "your_table",
  "fieldMapping": {"源字段": "目标字段"},
  "processingMode": "BATCH_ROWS",
  "targetRecordIds": ["rec123456"]
}
```

**特点**：
- ✅ 检测到单记录时自动优化
- ✅ 向后兼容
- ✅ 无需修改现有代码

## 📈 性能对比

| 方案 | 网络请求 | 数据传输量 | 处理时间 | 内存占用 |
|------|----------|------------|----------|----------|
| 传统BATCH_ROWS | 1次 | 全表数据 | 长 | 高 |
| SINGLE_RECORD+ID | 1次 | 单条记录 | 最短 | 最低 |
| SINGLE_RECORD+Index | 2次 | 少量+单条 | 短 | 低 |
| 自动优化 | 1次 | 单条记录 | 最短 | 最低 |

## 🛠️ 实际使用示例

### 示例1: 处理第1行数据

```bash
# 最优方式（如果知道记录ID）
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_RECORD",
    "targetRecordIds": ["rec123456"],
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }'

# 推荐方式（只知道行号）
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_RECORD",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }'
```

### 示例2: 处理第5行数据

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "LgSGbXDBsa0yCsshXujcN7WUnCh",
    "tableId": "tblUkixc2NcTbpmX",
    "fieldMapping": {
      "正☀️挂拍-SKC": "正☀️挂拍-URL"
    },
    "processingMode": "SINGLE_RECORD",
    "targetRowIndexes": 5,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }'
```

## 🔍 如何获取记录ID

### 方法1: 通过基础接口获取

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/records" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_token",
    "tableId": "your_table",
    "pageSize": 10
  }'
```

从响应中获取 `records[].recordId`

### 方法2: 通过增强接口获取

```bash
curl -X POST "http://localhost:8080/api/feishu/bitable/images/field-mapping-upload-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "your_token",
    "tableId": "your_table",
    "fieldMapping": {"任意字段": "任意字段"},
    "processingMode": "SINGLE_PAGE",
    "pageSize": 10,
    "summaryOnly": false
  }'
```

从响应中获取 `data.records[].recordId`

## 📋 最佳实践

### 1. 选择合适的模式

```javascript
// 如果知道记录ID
const request = {
  processingMode: "SINGLE_RECORD",
  targetRecordIds: ["rec123456"]
};

// 如果只知道行号
const request = {
  processingMode: "SINGLE_RECORD", 
  targetRowIndexes: 1
};

// 如果需要处理多行
const request = {
  processingMode: "BATCH_ROWS",
  targetRowIndexes: [1, 3, 5]
};
```

### 2. 缓存记录ID

```javascript
// 第一次获取时缓存记录ID
const recordIds = await getRecordIds();
localStorage.setItem('recordIds', JSON.stringify(recordIds));

// 后续使用缓存的记录ID
const cachedIds = JSON.parse(localStorage.getItem('recordIds'));
const request = {
  processingMode: "SINGLE_RECORD",
  targetRecordIds: [cachedIds[0]] // 使用第1行的记录ID
};
```

### 3. 错误处理

```javascript
try {
  const response = await fetch('/api/feishu/bitable/images/field-mapping-upload-enhanced', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      processingMode: "SINGLE_RECORD",
      targetRowIndexes: 1,
      // ... 其他参数
    })
  });
  
  if (!response.ok) {
    // 如果SINGLE_RECORD模式失败，回退到BATCH_ROWS
    const fallbackResponse = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        processingMode: "BATCH_ROWS",
        targetRowIndexes: 1,
        // ... 其他参数
      })
    });
  }
} catch (error) {
  console.error('请求失败:', error);
}
```

## 🎯 总结

通过新增的 `SINGLE_RECORD` 处理模式，您现在可以：

1. ✅ **精确获取单条记录** - 不再获取全部数据
2. ✅ **支持记录ID和行索引** - 灵活的定位方式  
3. ✅ **自动优化检测** - 智能选择最优方案
4. ✅ **向后兼容** - 现有代码无需修改
5. ✅ **显著提升性能** - 特别是大表格场景

**推荐使用方式**：
- 已知记录ID → `SINGLE_RECORD + targetRecordIds`
- 只知道行号 → `SINGLE_RECORD + targetRowIndexes`  
- 处理多行 → `BATCH_ROWS + targetRowIndexes`

这样就完美解决了您"不要一次性获取全部数据"的需求！🎉
