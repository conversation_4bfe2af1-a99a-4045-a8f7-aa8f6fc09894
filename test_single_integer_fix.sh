#!/bin/bash

# 测试单个整数值修复的脚本
# 专门测试 targetRowIndexes 字段是否能正确处理单个整数值

BASE_URL="http://localhost:8080"
ENDPOINT="/api/feishu/bitable/images/field-mapping-upload-enhanced"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}测试单个整数值修复${NC}"
echo -e "${BLUE}========================================${NC}"

# 测试用的表格信息（请根据实际情况修改）
APP_TOKEN="MgDxby4r7avigssLQnVcIQzJnm1"
TABLE_ID="tbl4sH8PYHUk36K0"

echo -e "\n${YELLOW}测试配置:${NC}"
echo -e "Base URL: ${BASE_URL}"
echo -e "Endpoint: ${ENDPOINT}"
echo -e "App Token: ${APP_TOKEN}"
echo -e "Table ID: ${TABLE_ID}"

# 测试1: 单个整数值（之前会报错的情况）
echo -e "\n${BLUE}测试1: 单个整数值 - targetRowIndexes: 1${NC}"
echo -e "${YELLOW}这个测试之前会报JSON反序列化错误，现在应该能正常工作${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": 1,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试1完成${NC}"

# 测试2: 数组值（确保原有功能仍然正常）
echo -e "\n${BLUE}测试2: 数组值 - targetRowIndexes: [1, 2, 3]${NC}"
echo -e "${YELLOW}确保数组值仍然能正常工作${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "BATCH_ROWS",
    "targetRowIndexes": [1, 2, 3],
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试2完成${NC}"

# 测试3: null值（确保null值处理正常）
echo -e "\n${BLUE}测试3: null值 - targetRowIndexes: null${NC}"
echo -e "${YELLOW}确保null值能正常处理${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "SINGLE_PAGE",
    "targetRowIndexes": null,
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试3完成${NC}"

# 测试4: 不传递该字段（确保可选字段处理正常）
echo -e "\n${BLUE}测试4: 不传递targetRowIndexes字段${NC}"
echo -e "${YELLOW}确保不传递该字段时能正常工作${NC}"

curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "appToken": "'${APP_TOKEN}'",
    "tableId": "'${TABLE_ID}'",
    "fieldMapping": {
      "👚上装正面图": "👚上装正面图url"
    },
    "processingMode": "SINGLE_PAGE",
    "updateBitableWithLocalUrl": true,
    "summaryOnly": true
  }' | jq '.'

echo -e "\n${GREEN}测试4完成${NC}"

echo -e "\n${BLUE}========================================${NC}"
echo -e "${GREEN}所有测试完成！${NC}"
echo -e "${BLUE}========================================${NC}"

echo -e "\n${YELLOW}测试说明:${NC}"
echo -e "1. 单个整数值: 测试 targetRowIndexes: 1 是否能正常工作"
echo -e "2. 数组值: 测试 targetRowIndexes: [1, 2, 3] 是否仍然正常"
echo -e "3. null值: 测试 targetRowIndexes: null 是否能正常处理"
echo -e "4. 不传递字段: 测试不传递该字段时是否正常"

echo -e "\n${YELLOW}期望结果:${NC}"
echo -e "- 所有测试都应该返回正常的响应，不应该有JSON反序列化错误"
echo -e "- 如果仍然有错误，请检查服务器日志获取详细信息"
