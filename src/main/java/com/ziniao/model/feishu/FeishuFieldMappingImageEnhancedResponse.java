package com.ziniao.model.feishu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格字段映射图片上传增强版响应
 */
@ApiModel(description = "飞书多维表格字段映射图片上传增强版响应")
public class FeishuFieldMappingImageEnhancedResponse {

    @ApiModelProperty(value = "响应状态码")
    private int code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "是否成功")
    private boolean success;

    @ApiModelProperty(value = "响应数据")
    private Data data;

    // 静态工厂方法
    public static FeishuFieldMappingImageEnhancedResponse success(Data data) {
        FeishuFieldMappingImageEnhancedResponse response = new FeishuFieldMappingImageEnhancedResponse();
        response.setCode(200);
        response.setMessage("处理成功");
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public static FeishuFieldMappingImageEnhancedResponse error(int code, String message) {
        FeishuFieldMappingImageEnhancedResponse response = new FeishuFieldMappingImageEnhancedResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }

    @ApiModel(description = "响应数据")
    public static class Data {
        @ApiModelProperty(value = "处理摘要")
        private ProcessingSummary summary;

        @ApiModelProperty(value = "分页信息")
        private PaginationInfo pagination;

        @ApiModelProperty(value = "详细处理结果（当summaryOnly=false时返回）")
        private List<RecordProcessingResult> records;

        @ApiModelProperty(value = "处理时间（毫秒）")
        private Long processingTime;

        @ApiModelProperty(value = "处理模式")
        private String processingMode;

        // Getters and Setters
        public ProcessingSummary getSummary() {
            return summary;
        }

        public void setSummary(ProcessingSummary summary) {
            this.summary = summary;
        }

        public PaginationInfo getPagination() {
            return pagination;
        }

        public void setPagination(PaginationInfo pagination) {
            this.pagination = pagination;
        }

        public List<RecordProcessingResult> getRecords() {
            return records;
        }

        public void setRecords(List<RecordProcessingResult> records) {
            this.records = records;
        }

        public Long getProcessingTime() {
            return processingTime;
        }

        public void setProcessingTime(Long processingTime) {
            this.processingTime = processingTime;
        }

        public String getProcessingMode() {
            return processingMode;
        }

        public void setProcessingMode(String processingMode) {
            this.processingMode = processingMode;
        }
    }

    @ApiModel(description = "处理摘要")
    public static class ProcessingSummary {
        @ApiModelProperty(value = "总处理记录数")
        private int totalRecords;

        @ApiModelProperty(value = "成功处理记录数")
        private int successfulRecords;

        @ApiModelProperty(value = "失败处理记录数")
        private int failedRecords;

        @ApiModelProperty(value = "跳过的记录数")
        private int skippedRecords;

        @ApiModelProperty(value = "总图片数")
        private int totalImages;

        @ApiModelProperty(value = "成功下载图片数")
        private int successfulDownloads;

        @ApiModelProperty(value = "失败下载图片数")
        private int failedDownloads;

        @ApiModelProperty(value = "跳过的图片数")
        private int skippedImages;

        @ApiModelProperty(value = "处理的字段映射数")
        private int processedFieldMappings;

        // Getters and Setters
        public int getTotalRecords() {
            return totalRecords;
        }

        public void setTotalRecords(int totalRecords) {
            this.totalRecords = totalRecords;
        }

        public int getSuccessfulRecords() {
            return successfulRecords;
        }

        public void setSuccessfulRecords(int successfulRecords) {
            this.successfulRecords = successfulRecords;
        }

        public int getFailedRecords() {
            return failedRecords;
        }

        public void setFailedRecords(int failedRecords) {
            this.failedRecords = failedRecords;
        }

        public int getSkippedRecords() {
            return skippedRecords;
        }

        public void setSkippedRecords(int skippedRecords) {
            this.skippedRecords = skippedRecords;
        }

        public int getTotalImages() {
            return totalImages;
        }

        public void setTotalImages(int totalImages) {
            this.totalImages = totalImages;
        }

        public int getSuccessfulDownloads() {
            return successfulDownloads;
        }

        public void setSuccessfulDownloads(int successfulDownloads) {
            this.successfulDownloads = successfulDownloads;
        }

        public int getFailedDownloads() {
            return failedDownloads;
        }

        public void setFailedDownloads(int failedDownloads) {
            this.failedDownloads = failedDownloads;
        }

        public int getSkippedImages() {
            return skippedImages;
        }

        public void setSkippedImages(int skippedImages) {
            this.skippedImages = skippedImages;
        }

        public int getProcessedFieldMappings() {
            return processedFieldMappings;
        }

        public void setProcessedFieldMappings(int processedFieldMappings) {
            this.processedFieldMappings = processedFieldMappings;
        }
    }

    @ApiModel(description = "分页信息")
    public static class PaginationInfo {
        @ApiModelProperty(value = "当前页大小")
        private int currentPageSize;

        @ApiModelProperty(value = "是否还有更多数据")
        private boolean hasMore;

        @ApiModelProperty(value = "下一页标记")
        private String nextPageToken;

        @ApiModelProperty(value = "已处理页数")
        private int processedPages;

        @ApiModelProperty(value = "总页数（如果已知）")
        private Integer totalPages;

        // Getters and Setters
        public int getCurrentPageSize() {
            return currentPageSize;
        }

        public void setCurrentPageSize(int currentPageSize) {
            this.currentPageSize = currentPageSize;
        }

        public boolean isHasMore() {
            return hasMore;
        }

        public void setHasMore(boolean hasMore) {
            this.hasMore = hasMore;
        }

        public String getNextPageToken() {
            return nextPageToken;
        }

        public void setNextPageToken(String nextPageToken) {
            this.nextPageToken = nextPageToken;
        }

        public int getProcessedPages() {
            return processedPages;
        }

        public void setProcessedPages(int processedPages) {
            this.processedPages = processedPages;
        }

        public Integer getTotalPages() {
            return totalPages;
        }

        public void setTotalPages(Integer totalPages) {
            this.totalPages = totalPages;
        }
    }

    @ApiModel(description = "记录处理结果")
    public static class RecordProcessingResult {
        @ApiModelProperty(value = "记录ID")
        private String recordId;

        @ApiModelProperty(value = "行索引")
        private Integer rowIndex;

        @ApiModelProperty(value = "处理状态：SUCCESS, FAILED, SKIPPED")
        private String status;

        @ApiModelProperty(value = "处理消息")
        private String message;

        @ApiModelProperty(value = "字段处理结果")
        private Map<String, FieldProcessingResult> fieldResults;

        @ApiModelProperty(value = "处理的图片数")
        private int imageCount;

        @ApiModelProperty(value = "成功处理的图片数")
        private int successCount;

        @ApiModelProperty(value = "失败处理的图片数")
        private int failureCount;

        // Getters and Setters
        public String getRecordId() {
            return recordId;
        }

        public void setRecordId(String recordId) {
            this.recordId = recordId;
        }

        public Integer getRowIndex() {
            return rowIndex;
        }

        public void setRowIndex(Integer rowIndex) {
            this.rowIndex = rowIndex;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Map<String, FieldProcessingResult> getFieldResults() {
            return fieldResults;
        }

        public void setFieldResults(Map<String, FieldProcessingResult> fieldResults) {
            this.fieldResults = fieldResults;
        }

        public int getImageCount() {
            return imageCount;
        }

        public void setImageCount(int imageCount) {
            this.imageCount = imageCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }
    }

    @ApiModel(description = "字段处理结果")
    public static class FieldProcessingResult {
        @ApiModelProperty(value = "源字段名")
        private String sourceField;

        @ApiModelProperty(value = "目标字段名")
        private String targetField;

        @ApiModelProperty(value = "处理状态")
        private String status;

        @ApiModelProperty(value = "处理消息")
        private String message;

        @ApiModelProperty(value = "图片数量")
        private int imageCount;

        @ApiModelProperty(value = "本地URL列表")
        private List<String> localUrls;

        // Getters and Setters
        public String getSourceField() {
            return sourceField;
        }

        public void setSourceField(String sourceField) {
            this.sourceField = sourceField;
        }

        public String getTargetField() {
            return targetField;
        }

        public void setTargetField(String targetField) {
            this.targetField = targetField;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getImageCount() {
            return imageCount;
        }

        public void setImageCount(int imageCount) {
            this.imageCount = imageCount;
        }

        public List<String> getLocalUrls() {
            return localUrls;
        }

        public void setLocalUrls(List<String> localUrls) {
            this.localUrls = localUrls;
        }
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }
}
