package com.ziniao.model.feishu;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格字段映射图片上传增强版请求参数
 * 支持批量行处理、智能分页、性能优化等功能
 */
@ApiModel(description = "飞书多维表格字段映射图片上传增强版请求参数")
public class FeishuFieldMappingImageEnhancedRequest {

    @ApiModelProperty(value = "多维表格的唯一标识符", required = true, example = "MgDxby4r7avigssLQnVcIQzJnm1")
    @NotBlank(message = "app_token不能为空")
    private String appToken;

    @ApiModelProperty(value = "数据表的唯一标识符", required = true, example = "tbl4sH8PYHUk36K0")
    @NotBlank(message = "table_id不能为空")
    private String tableId;

    @ApiModelProperty(value = "视图的唯一标识符，不传则使用默认视图", example = "vewgI30A6c")
    private String viewId;

    @ApiModelProperty(value = "字段映射关系，key为源图片字段名，value为目标URL字段名", 
                     required = true,
                     example = "{\"👚上装正面图\": \"👚上装正面图url\", \"👚上装背面图\": \"👚上装背面图url\"}")
    @NotEmpty(message = "字段映射关系不能为空")
    private Map<String, String> fieldMapping;

    // ========== 行选择参数 ==========
    @ApiModelProperty(value = "指定要处理的行索引列表（从1开始），支持批量处理多行，可以传单个数字或数组",
                     example = "[1, 3, 5, 10]")
    @JsonDeserialize(using = IntegerListDeserializer.class)
    private List<Integer> targetRowIndexes;

    @ApiModelProperty(value = "指定要处理的记录ID列表，支持批量处理多个记录", 
                     example = "[\"recqwIwhc6\", \"recABC123\", \"recXYZ789\"]")
    private List<String> targetRecordIds;

    @ApiModelProperty(value = "行范围处理：起始行号（从1开始）", example = "1")
    private Integer startRowIndex;

    @ApiModelProperty(value = "行范围处理：结束行号（包含）", example = "100")
    private Integer endRowIndex;

    // ========== 分页控制参数 ==========
    @ApiModelProperty(value = "分页大小，最大值是 500。建议：少量数据用50-100，大量数据用200-500", example = "100")
    private Integer pageSize = 100;

    @ApiModelProperty(value = "分页标记，第一次请求不填", example = "recqwIwhc6")
    private String pageToken;

    @ApiModelProperty(value = "是否启用自动分页处理，true时会自动处理所有分页数据", example = "false")
    private Boolean autoHandlePagination = false;

    @ApiModelProperty(value = "自动分页时的最大处理页数，防止无限循环", example = "10")
    private Integer maxPagesToProcess = 10;

    // ========== 性能优化参数 ==========
    @ApiModelProperty(value = "是否只返回处理结果摘要，不返回详细的图片信息（提升性能）", example = "false")
    private Boolean summaryOnly = false;

    @ApiModelProperty(value = "图片下载超时时间（秒）", example = "30")
    private Integer downloadTimeout = 30;

    @ApiModelProperty(value = "最大并发下载数", example = "5")
    private Integer maxConcurrentDownloads = 5;

    @ApiModelProperty(value = "批处理大小，每批处理的记录数", example = "10")
    private Integer batchSize = 10;

    // ========== 功能控制参数 ==========
    @ApiModelProperty(value = "是否将本地URL写回多维表格", example = "true")
    private Boolean updateBitableWithLocalUrl = true;

    @ApiModelProperty(value = "是否返回图片的详细信息", example = "true")
    private Boolean includeImageDetails = true;

    @ApiModelProperty(value = "筛选条件，用于筛选要处理的记录")
    private String filter;

    @ApiModelProperty(value = "是否跳过已有本地URL的字段（避免重复处理）", example = "true")
    private Boolean skipExistingUrls = true;

    @ApiModelProperty(value = "处理模式：SINGLE_PAGE(单页处理), BATCH_ROWS(批量行处理), AUTO_PAGINATION(自动分页)", 
                     example = "SINGLE_PAGE")
    private String processingMode = "SINGLE_PAGE";

    // ========== Getters and Setters ==========
    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public String getViewId() {
        return viewId;
    }

    public void setViewId(String viewId) {
        this.viewId = viewId;
    }

    public Map<String, String> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, String> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    public List<Integer> getTargetRowIndexes() {
        return targetRowIndexes;
    }

    public void setTargetRowIndexes(List<Integer> targetRowIndexes) {
        this.targetRowIndexes = targetRowIndexes;
    }

    public List<String> getTargetRecordIds() {
        return targetRecordIds;
    }

    public void setTargetRecordIds(List<String> targetRecordIds) {
        this.targetRecordIds = targetRecordIds;
    }

    public Integer getStartRowIndex() {
        return startRowIndex;
    }

    public void setStartRowIndex(Integer startRowIndex) {
        this.startRowIndex = startRowIndex;
    }

    public Integer getEndRowIndex() {
        return endRowIndex;
    }

    public void setEndRowIndex(Integer endRowIndex) {
        this.endRowIndex = endRowIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }

    public Boolean getAutoHandlePagination() {
        return autoHandlePagination;
    }

    public void setAutoHandlePagination(Boolean autoHandlePagination) {
        this.autoHandlePagination = autoHandlePagination;
    }

    public Integer getMaxPagesToProcess() {
        return maxPagesToProcess;
    }

    public void setMaxPagesToProcess(Integer maxPagesToProcess) {
        this.maxPagesToProcess = maxPagesToProcess;
    }

    public Boolean getSummaryOnly() {
        return summaryOnly;
    }

    public void setSummaryOnly(Boolean summaryOnly) {
        this.summaryOnly = summaryOnly;
    }

    public Integer getDownloadTimeout() {
        return downloadTimeout;
    }

    public void setDownloadTimeout(Integer downloadTimeout) {
        this.downloadTimeout = downloadTimeout;
    }

    public Integer getMaxConcurrentDownloads() {
        return maxConcurrentDownloads;
    }

    public void setMaxConcurrentDownloads(Integer maxConcurrentDownloads) {
        this.maxConcurrentDownloads = maxConcurrentDownloads;
    }

    public Integer getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public Boolean getUpdateBitableWithLocalUrl() {
        return updateBitableWithLocalUrl;
    }

    public void setUpdateBitableWithLocalUrl(Boolean updateBitableWithLocalUrl) {
        this.updateBitableWithLocalUrl = updateBitableWithLocalUrl;
    }

    public Boolean getIncludeImageDetails() {
        return includeImageDetails;
    }

    public void setIncludeImageDetails(Boolean includeImageDetails) {
        this.includeImageDetails = includeImageDetails;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Boolean getSkipExistingUrls() {
        return skipExistingUrls;
    }

    public void setSkipExistingUrls(Boolean skipExistingUrls) {
        this.skipExistingUrls = skipExistingUrls;
    }

    public String getProcessingMode() {
        return processingMode;
    }

    public void setProcessingMode(String processingMode) {
        this.processingMode = processingMode;
    }

    @Override
    public String toString() {
        return "FeishuFieldMappingImageEnhancedRequest{" +
                "appToken='" + appToken + '\'' +
                ", tableId='" + tableId + '\'' +
                ", viewId='" + viewId + '\'' +
                ", fieldMapping=" + fieldMapping +
                ", targetRowIndexes=" + targetRowIndexes +
                ", targetRecordIds=" + targetRecordIds +
                ", startRowIndex=" + startRowIndex +
                ", endRowIndex=" + endRowIndex +
                ", pageSize=" + pageSize +
                ", pageToken='" + pageToken + '\'' +
                ", autoHandlePagination=" + autoHandlePagination +
                ", maxPagesToProcess=" + maxPagesToProcess +
                ", summaryOnly=" + summaryOnly +
                ", downloadTimeout=" + downloadTimeout +
                ", maxConcurrentDownloads=" + maxConcurrentDownloads +
                ", batchSize=" + batchSize +
                ", updateBitableWithLocalUrl=" + updateBitableWithLocalUrl +
                ", includeImageDetails=" + includeImageDetails +
                ", filter='" + filter + '\'' +
                ", skipExistingUrls=" + skipExistingUrls +
                ", processingMode='" + processingMode + '\'' +
                '}';
    }

    /**
     * 自定义反序列化器，支持将单个整数或整数数组反序列化为List<Integer>
     */
    public static class IntegerListDeserializer extends JsonDeserializer<List<Integer>> {
        @Override
        public List<Integer> deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            List<Integer> result = new ArrayList<>();

            JsonToken token = parser.getCurrentToken();
            if (token == JsonToken.VALUE_NUMBER_INT) {
                // 单个整数值
                result.add(parser.getIntValue());
            } else if (token == JsonToken.START_ARRAY) {
                // 数组值
                while (parser.nextToken() != JsonToken.END_ARRAY) {
                    if (parser.getCurrentToken() == JsonToken.VALUE_NUMBER_INT) {
                        result.add(parser.getIntValue());
                    }
                }
            } else if (token == JsonToken.VALUE_NULL) {
                // null值
                return null;
            }

            return result;
        }
    }
}
